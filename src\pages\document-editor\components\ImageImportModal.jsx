import React, { useState, useEffect, useRef } from 'react';
import Button from '../../../components/ui/Button';

/**
 * ImageImportModal - Unified modal for image import with Upload and Collections tabs
 * Provides both file upload functionality and AI-suggested image selection
 */
const ImageImportModal = ({
  isOpen,
  onClose,
  onImageSelect,
  imageSuggestions = {},
  chapterId = null,
  isReviewMode = false
}) => {
  const [activeTab, setActiveTab] = useState('upload');
  const [selectedImage, setSelectedImage] = useState(null);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadError, setUploadError] = useState('');
  const [previewUrl, setPreviewUrl] = useState(null);
  const fileInputRef = useRef(null);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setSelectedImage(null);
      setUploadedFile(null);
      setIsProcessing(false);
      setIsDragOver(false);
      setUploadError('');
      setPreviewUrl(null);
      // Default to upload tab, but switch to collections if AI suggestions exist
      const hasAISuggestions = imageSuggestions && Object.keys(imageSuggestions).length > 0;
      setActiveTab(hasAISuggestions ? 'collections' : 'upload');
    }
  }, [isOpen, imageSuggestions]);

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, onClose]);

  // Don't render modal in review mode
  if (!isOpen || isReviewMode) return null;

  // Get images from suggestions for collections tab
  const availableImages = chapterId && imageSuggestions[chapterId]
    ? imageSuggestions[chapterId].images || []
    : Object.values(imageSuggestions).flatMap(chapter => chapter.images || []);

  const hasAISuggestions = availableImages.length > 0;

  const handleTabSwitch = (tab) => {
    setActiveTab(tab);
    setSelectedImage(null);
    setUploadedFile(null);
    setUploadError('');
    setPreviewUrl(null);
  };

  const handleImageSelect = (image) => {
    setSelectedImage(image);
    setUploadedFile(null);
    setPreviewUrl(null);
  };

  // Image file validation
  const validateImageFile = (file) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    const maxSize = 10 * 1024 * 1024; // 10MB

    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Please select a valid image file (JPG, PNG, GIF, or WebP)'
      };
    }

    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'File size must be less than 10MB'
      };
    }

    return { isValid: true, error: '' };
  };

  // Handle file selection and validation
  const handleFileSelect = (file) => {
    const validation = validateImageFile(file);

    if (validation.isValid) {
      setUploadedFile(file);
      setSelectedImage(null);
      setUploadError('');

      // Create preview URL
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    } else {
      setUploadedFile(null);
      setPreviewUrl(null);
      setUploadError(validation.error);
    }
  };

  // Drag and drop handlers
  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  // Handle file input change
  const handleFileInputChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  // Trigger file input click
  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Clear uploaded file
  const clearUploadedFile = () => {
    setUploadedFile(null);
    setPreviewUrl(null);
    setUploadError('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Enhanced image processing with optimization and error handling
  const processUploadedImage = async (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = async (e) => {
        try {
          const dataUrl = e.target.result;

          // Create an image element to get dimensions and potentially resize
          const img = new Image();
          img.onload = async () => {
            try {
              let finalDataUrl = dataUrl;

              // Check if image needs resizing (optional optimization)
              const maxWidth = 1920;
              const maxHeight = 1080;

              if (img.width > maxWidth || img.height > maxHeight) {
                finalDataUrl = await resizeImage(img, maxWidth, maxHeight, file.type);
                console.log(`🖼️ Image resized from ${img.width}x${img.height} to fit ${maxWidth}x${maxHeight}`);
              }

              const imageData = {
                src: finalDataUrl,
                alt: generateAltText(file.name),
                title: file.name,
                originalSize: file.size,
                dimensions: {
                  width: img.width,
                  height: img.height
                }
              };

              resolve(imageData);
            } catch (resizeError) {
              console.warn('Image resize failed, using original:', resizeError);
              // Fallback to original image if resize fails
              resolve({
                src: dataUrl,
                alt: generateAltText(file.name),
                title: file.name,
                originalSize: file.size
              });
            }
          };

          img.onerror = () => {
            reject(new Error('Failed to load image for processing'));
          };

          img.src = dataUrl;
        } catch (error) {
          reject(error);
        }
      };

      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };

      reader.readAsDataURL(file);
    });
  };

  // Helper function to resize image if needed
  const resizeImage = (img, maxWidth, maxHeight, mimeType) => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Calculate new dimensions maintaining aspect ratio
      let { width, height } = img;

      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx.drawImage(img, 0, 0, width, height);

      // Convert back to data URL with quality compression for JPEG
      const quality = mimeType === 'image/jpeg' ? 0.8 : undefined;
      const resizedDataUrl = canvas.toDataURL(mimeType, quality);

      resolve(resizedDataUrl);
    });
  };

  // Helper function to generate better alt text from filename
  const generateAltText = (filename) => {
    return filename
      .replace(/\.[^/.]+$/, '') // Remove extension
      .replace(/[-_]/g, ' ') // Replace hyphens and underscores with spaces
      .replace(/\b\w/g, l => l.toUpperCase()) // Capitalize first letter of each word
      .trim();
  };

  const handleConfirmSelection = async () => {
    if (activeTab === 'upload' && uploadedFile) {
      setIsProcessing(true);
      setUploadError('');

      try {
        console.log('🔄 Processing uploaded image:', uploadedFile.name);
        const imageData = await processUploadedImage(uploadedFile);
        console.log('✅ Image processed successfully:', imageData);

        onImageSelect(imageData);
        onClose();
      } catch (error) {
        console.error('❌ Error processing uploaded image:', error);
        setUploadError(`Failed to process image: ${error.message}`);
      } finally {
        setIsProcessing(false);
      }
    } else if (activeTab === 'collections' && selectedImage) {
      console.log('🎨 Using AI-suggested image:', selectedImage);
      onImageSelect(selectedImage);
      onClose();
    }
  };

  const canConfirm = (activeTab === 'upload' && uploadedFile) || (activeTab === 'collections' && selectedImage);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Add Image</h2>
            <p className="text-sm text-gray-600 mt-1">
              Upload your own image or choose from AI suggestions
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            <button
              onClick={() => handleTabSwitch('upload')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'upload'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Upload
            </button>
            <button
              onClick={() => handleTabSwitch('collections')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'collections'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Collections {hasAISuggestions && `(${availableImages.length})`}
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        <div className="flex-1 overflow-auto p-6">
          {activeTab === 'upload' ? (
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-gray-900">Upload Image</h3>
              <p className="text-sm text-gray-600">
                Select an image file from your computer (JPG, PNG, GIF, WebP)
              </p>

              {/* Hidden file input */}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
                onChange={handleFileInputChange}
                className="hidden"
              />

              {/* Upload area */}
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-all cursor-pointer ${
                  isDragOver
                    ? 'border-blue-500 bg-blue-50'
                    : uploadedFile
                    ? 'border-green-300 bg-green-50'
                    : uploadError
                    ? 'border-red-300 bg-red-50'
                    : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={triggerFileInput}
              >
                <div className="space-y-4">
                  {/* Icon */}
                  {uploadedFile ? (
                    <svg className="w-12 h-12 mx-auto text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  ) : uploadError ? (
                    <svg className="w-12 h-12 mx-auto text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  ) : (
                    <svg className="w-12 h-12 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                  )}

                  {/* Main message */}
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-2">
                      {uploadedFile ? 'Image Selected' : uploadError ? 'Upload Error' : 'Upload Image'}
                    </h4>
                    <p className="text-gray-600">
                      {uploadedFile
                        ? `${uploadedFile.name} (${(uploadedFile.size / 1024 / 1024).toFixed(2)} MB)`
                        : uploadError
                        ? uploadError
                        : 'Drag and drop your image here, or click to browse'
                      }
                    </p>
                  </div>

                  {/* File requirements */}
                  {!uploadedFile && !uploadError && (
                    <div className="text-sm text-gray-500 space-y-1">
                      <p>• Supported formats: JPG, PNG, GIF, WebP</p>
                      <p>• Maximum file size: 10MB</p>
                    </div>
                  )}

                  {/* Clear button for uploaded file */}
                  {uploadedFile && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        clearUploadedFile();
                      }}
                      className="inline-flex items-center px-3 py-2 text-sm font-medium text-red-600 bg-white border border-red-300 rounded-md hover:bg-red-50"
                    >
                      Clear File
                    </button>
                  )}
                </div>
              </div>

              {/* Image preview */}
              {previewUrl && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Preview</h4>
                  <div className="border border-gray-200 rounded-lg overflow-hidden max-w-md">
                    <img
                      src={previewUrl}
                      alt="Preview"
                      className="w-full h-48 object-cover"
                    />
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {hasAISuggestions ? (
                <>
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium text-gray-900">
                      AI-Suggested Images
                    </h3>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                      {availableImages.length} available
                    </span>
                  </div>

                  <p className="text-sm text-gray-600">
                    These images were generated based on your document content and are ready to use.
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {availableImages.map((image, index) => (
                      <div
                        key={image.id || index}
                        className={`relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${
                          selectedImage?.id === image.id
                            ? 'border-blue-500 shadow-lg ring-2 ring-blue-200'
                            : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
                        }`}
                        onClick={() => handleImageSelect(image)}
                      >
                        <div className="aspect-video">
                          <img
                            src={image.thumbnailUrl || image.url}
                            alt={image.description || `AI suggested image ${index + 1}`}
                            className="w-full h-full object-cover"
                            loading="lazy"
                          />
                        </div>

                        {/* Hover overlay */}
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all" />

                        {/* Selection indicator */}
                        {selectedImage?.id === image.id && (
                          <div className="absolute top-2 right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </div>
                        )}

                        {/* Image info overlay */}
                        {image.description && (
                          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-3">
                            <p className="text-white text-xs line-clamp-2">
                              {image.description}
                            </p>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Additional info */}
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-start">
                      <svg className="w-5 h-5 text-blue-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div className="text-sm">
                        <p className="text-blue-800 font-medium">AI-Generated Images</p>
                        <p className="text-blue-700 mt-1">
                          These images are contextually relevant to your document content and optimized for professional use.
                        </p>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="text-center py-12">
                  <svg className="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No AI suggestions available</h3>
                  <p className="text-gray-600 mb-4">
                    AI image suggestions will appear here when available for your document.
                  </p>
                  <button
                    onClick={() => handleTabSwitch('upload')}
                    className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    Upload Your Own Image
                  </button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div className="text-sm text-gray-600">
            {activeTab === 'upload' && uploadedFile ? (
              <span>Selected: {uploadedFile.name} ({(uploadedFile.size / 1024 / 1024).toFixed(2)} MB)</span>
            ) : activeTab === 'collections' && selectedImage ? (
              <span>Selected: {selectedImage.description}</span>
            ) : uploadError ? (
              <span className="text-red-600">{uploadError}</span>
            ) : (
              <span>Select an image to continue</span>
            )}
          </div>
          <div className="flex space-x-3">
            <Button variant="secondary" onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={handleConfirmSelection}
              disabled={!canConfirm || isProcessing || !!uploadError}
            >
              {isProcessing ? 'Processing...' : 'Add Image'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImageImportModal;
