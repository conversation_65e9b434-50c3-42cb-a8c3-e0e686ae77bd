# Image Import Modal - Manual Test Script

## Quick Verification Steps

### 1. Basic Modal Functionality (5 minutes)

1. **Open the document editor**
   - Navigate to a document in edit mode
   - Click in an empty paragraph to show the plus menu

2. **Test modal opening**
   ```
   ✓ Click the 🖼️ "Image" button in the plus menu
   ✓ Verify ImageImportModal opens
   ✓ Check both "Upload" and "Collections" tabs are visible
   ✓ Press Escape to close modal
   ```

3. **Test tab switching**
   ```
   ✓ Reopen modal
   ✓ Click "Collections" tab
   ✓ Click "Upload" tab
   ✓ Verify content changes appropriately
   ```

### 2. Upload Functionality Test (10 minutes)

1. **Prepare test images**
   - Small JPG (< 1MB)
   - Large PNG (2-5MB)
   - Invalid file (PDF or TXT)

2. **Test drag and drop**
   ```
   ✓ Open modal, ensure Upload tab is active
   ✓ Drag JPG file over upload area
   ✓ Verify blue highlight appears
   ✓ Drop file and check preview appears
   ✓ Verify file name and size shown
   ✓ Click "Add Image" button
   ✓ Confirm image inserts into editor
   ```

3. **Test click to browse**
   ```
   ✓ Open modal again
   ✓ Click upload area
   ✓ Select PNG file from file dialog
   ✓ Verify preview and details appear
   ✓ Test "Clear File" button
   ✓ Upload file again and insert
   ```

4. **Test error handling**
   ```
   ✓ Try uploading PDF file
   ✓ Verify error message appears
   ✓ Try uploading very large file (>10MB)
   ✓ Check appropriate error shown
   ```

### 3. Collections Functionality Test (5 minutes)

**Note**: This requires AI suggestions to be available in the document.

1. **With AI suggestions**
   ```
   ✓ Open modal
   ✓ Click "Collections" tab
   ✓ Verify AI images display in grid
   ✓ Click an image to select it
   ✓ Verify blue border and checkmark appear
   ✓ Click "Add Image" to insert
   ```

2. **Without AI suggestions**
   ```
   ✓ Test with document that has no AI suggestions
   ✓ Verify empty state message appears
   ✓ Test "Upload Your Own Image" button
   ✓ Confirm it switches to Upload tab
   ```

### 4. Integration Verification (5 minutes)

1. **Editor integration**
   ```
   ✓ Insert multiple images using both upload and collections
   ✓ Verify images appear at correct cursor positions
   ✓ Check images have proper alt text
   ✓ Test images display correctly in editor
   ```

2. **State management**
   ```
   ✓ Open modal, select file, close without inserting
   ✓ Reopen modal - verify state is reset
   ✓ Switch between tabs multiple times
   ✓ Confirm no state leakage between tabs
   ```

## Browser Console Verification

Open browser DevTools and check for:

```javascript
// Expected console messages:
"🔄 Processing uploaded image: [filename]"
"✅ Image processed successfully: [object]"
"🖼️ Image selected from import modal: [object]"
"✅ Image inserted successfully"

// No error messages should appear
```

## Quick Visual Checks

### Upload Tab
- [ ] Upload area has dashed border
- [ ] Drag hover shows blue highlight
- [ ] File preview shows correctly
- [ ] Error states show red styling
- [ ] Success states show green styling

### Collections Tab
- [ ] Images display in responsive grid
- [ ] Hover effects work smoothly
- [ ] Selection shows blue border + checkmark
- [ ] Empty state shows helpful message

### Modal Behavior
- [ ] Modal centers properly on screen
- [ ] Close button works
- [ ] Footer shows correct selection info
- [ ] Buttons enable/disable appropriately

## Expected Results

After completing these tests, you should have:

1. ✅ **Working upload functionality** - Files upload, process, and insert correctly
2. ✅ **Working collections display** - AI suggestions show and can be selected
3. ✅ **Proper error handling** - Invalid files show clear error messages
4. ✅ **Smooth user experience** - Modal behavior is intuitive and responsive
5. ✅ **Clean editor integration** - Images insert at correct positions with proper attributes

## Troubleshooting

### If modal doesn't open:
- Check browser console for errors
- Verify DocumentCanvasMinimal imported ImageImportModal correctly
- Ensure state management is working

### If upload doesn't work:
- Check file type validation
- Verify FileReader API support
- Test with different image formats

### If collections don't show:
- Verify imageSuggestions prop has data
- Check image URLs are accessible
- Ensure proper data structure

### If images don't insert:
- Check Tiptap editor integration
- Verify image extension is loaded
- Test with simpler image data

## Performance Notes

- Large images (>5MB) may take a few seconds to process
- Image resizing happens automatically for very large images
- Preview generation should be near-instant for normal-sized images
- Modal should open/close smoothly without lag
