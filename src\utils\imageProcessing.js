/**
 * Image Processing Utilities
 * 
 * Centralized image processing functions for downloading, validating, and converting images.
 * Extracted from contentProcessingService.js and errorHandlingService.js to eliminate duplication.
 */

import { validateImageUrl, validateImageFormat, validateFileSize, IMAGE_CONFIG } from './validation.js';
import { createLogger } from './logger.js';

// Create logger for this module
const logger = createLogger('ImageProcessing');

/**
 * Sleep utility for retry delays
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise} Promise that resolves after the delay
 */
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Download image with retry logic and comprehensive error handling
 * @param {string} url - Image URL to download
 * @param {number} retryCount - Current retry attempt (0-based)
 * @returns {Promise<Object>} Download result with success status and data/error
 */
export const downloadImage = async (url, retryCount = 0) => {
    // Validate URL first
    const urlValidation = validateImageUrl(url);
    if (!urlValidation.isValid) {
        const error = new Error(urlValidation.error);
        error.retryable = false;
        return {
            success: false,
            error: urlValidation.error,
            originalError: error,
            errorType: 'validation_error',
            url,
            attempts: retryCount + 1,
            retryable: false
        };
    }

    try {
        // Create AbortController for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), IMAGE_CONFIG.TIMEOUT);

        const response = await fetch(urlValidation.url, {
            method: 'GET',
            signal: controller.signal,
            headers: {
                'Accept': 'image/*',
                'User-Agent': 'DocForge-AI/1.0',
                'Cache-Control': 'no-cache'
            }
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
            error.status = response.status;
            error.statusText = response.statusText;
            throw error;
        }

        // Validate content type
        const contentType = response.headers.get('content-type');
        const formatValidation = validateImageFormat(contentType);

        if (!formatValidation.isValid) {
            const error = new Error(formatValidation.error);
            error.retryable = false;
            throw error;
        }

        // Check content length
        const contentLength = response.headers.get('content-length');
        if (contentLength && parseInt(contentLength) > IMAGE_CONFIG.MAX_SIZE) {
            const error = new Error(`Image too large: ${contentLength} bytes (max: ${IMAGE_CONFIG.MAX_SIZE} bytes)`);
            error.retryable = false;
            throw error;
        }

        // Download the image data
        const arrayBuffer = await response.arrayBuffer();

        // Final size check
        const sizeValidation = validateFileSize(arrayBuffer.byteLength);
        if (!sizeValidation.isValid) {
            const error = new Error(sizeValidation.error);
            error.retryable = false;
            throw error;
        }

        return {
            success: true,
            data: arrayBuffer,
            contentType: formatValidation.format,
            extension: formatValidation.extension,
            size: arrayBuffer.byteLength,
            url: urlValidation.url,
            attempts: retryCount + 1
        };

    } catch (error) {
        const isRetryable = shouldRetryImageDownload(error, retryCount);
        
        return {
            success: false,
            error: error.message,
            originalError: error,
            errorType: categorizeImageError(error),
            url: urlValidation.url,
            attempts: retryCount + 1,
            retryable: isRetryable
        };
    }
};

/**
 * Download image with automatic retry logic
 * @param {string} url - Image URL to download
 * @param {Object} options - Download options
 * @returns {Promise<Object>} Final download result
 */
export const downloadImageWithRetry = async (url, options = {}) => {
    const { maxRetries = IMAGE_CONFIG.MAX_RETRIES, baseDelay = IMAGE_CONFIG.RETRY_DELAY_BASE } = options;
    
    let lastError = null;
    let attempts = 0;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
        attempts = attempt + 1;
        
        const result = await downloadImage(url, attempt);
        
        if (result.success) {
            return result;
        }

        lastError = result;

        // Don't retry if error is not retryable or we've reached max retries
        if (!result.retryable || attempt >= maxRetries) {
            break;
        }

        // Calculate delay with exponential backoff
        const delay = baseDelay * Math.pow(2, attempt);
        logger.debug(`Retrying image download in ${delay}ms (attempt ${attempt + 1}/${maxRetries + 1})`);
        await sleep(delay);
    }

    // If we got here, all attempts failed
    return {
        success: false,
        error: lastError?.error || 'Image download failed after multiple attempts',
        originalError: lastError?.originalError,
        errorType: lastError?.errorType || 'unknown_error',
        url,
        attempts,
        retryable: false
    };
};

/**
 * Determine if an image download error should be retried
 * @param {Error} error - The error that occurred
 * @param {number} retryCount - Current retry count
 * @returns {boolean} Whether the error is retryable
 */
export const shouldRetryImageDownload = (error, retryCount) => {
    // Don't retry if we've exceeded max retries
    if (retryCount >= IMAGE_CONFIG.MAX_RETRIES) {
        return false;
    }

    // Don't retry certain types of errors
    if (error.retryable === false) {
        return false;
    }

    // Don't retry client errors (4xx)
    if (error.status >= 400 && error.status < 500) {
        return false;
    }

    // Don't retry validation errors
    if (error.message.includes('Invalid URL') || 
        error.message.includes('Unsupported image format') ||
        error.message.includes('too large')) {
        return false;
    }

    // Retry network errors, timeouts, and server errors (5xx)
    return true;
};

/**
 * Categorize image processing errors
 * @param {Error} error - The error to categorize
 * @returns {string} Error category
 */
export const categorizeImageError = (error) => {
    if (error.name === 'AbortError') {
        return 'timeout_error';
    }
    
    if (error.message.includes('Invalid URL')) {
        return 'validation_error';
    }
    
    if (error.message.includes('Unsupported image format')) {
        return 'format_error';
    }
    
    if (error.message.includes('too large')) {
        return 'size_error';
    }
    
    if (error.status >= 400 && error.status < 500) {
        return 'client_error';
    }
    
    if (error.status >= 500) {
        return 'server_error';
    }
    
    if (error.message.includes('fetch')) {
        return 'network_error';
    }
    
    return 'unknown_error';
};

/**
 * Calculate optimal image dimensions for DOCX
 * @param {Object} image - Image object with width/height
 * @param {Object} options - Sizing options
 * @returns {Object} Calculated dimensions
 */
export const calculateImageDimensions = (image, options = {}) => {
    const { maxWidth = IMAGE_CONFIG.DEFAULT_WIDTH, maxHeight = IMAGE_CONFIG.DEFAULT_HEIGHT } = options;
    
    let { width = maxWidth, height = maxHeight } = image;
    
    // If no dimensions provided, use defaults
    if (!width || !height) {
        return { width: maxWidth, height: maxHeight };
    }
    
    // Calculate aspect ratio
    const aspectRatio = width / height;
    
    // Scale down if too large
    if (width > maxWidth) {
        width = maxWidth;
        height = width / aspectRatio;
    }
    
    if (height > maxHeight) {
        height = maxHeight;
        width = height * aspectRatio;
    }
    
    return {
        width: Math.round(width),
        height: Math.round(height)
    };
};

/**
 * Convert image data for DOCX compatibility
 * @param {ArrayBuffer} imageData - Raw image data
 * @param {string} contentType - Image content type
 * @returns {Object} Conversion result
 */
export const convertImageForDocx = async (imageData, contentType) => {
    try {
        // For now, we'll pass through supported formats
        // Future enhancement: convert unsupported formats
        const formatValidation = validateImageFormat(contentType);
        
        if (formatValidation.isValid) {
            return {
                success: true,
                data: imageData,
                contentType: formatValidation.format
            };
        }

        // For unsupported formats, we'll need to convert them
        // This is a placeholder for future image conversion functionality
        logger.warn(`Image format ${contentType} may not be fully supported in DOCX`);

        return {
            success: true,
            data: imageData,
            contentType: contentType,
            warning: `Format ${contentType} may not display correctly in all DOCX viewers`
        };

    } catch (error) {
        return {
            success: false,
            error: `Image conversion failed: ${error.message}`
        };
    }
};

// Export all image processing functions
export default {
    downloadImage,
    downloadImageWithRetry,
    shouldRetryImageDownload,
    categorizeImageError,
    calculateImageDimensions,
    convertImageForDocx
};
