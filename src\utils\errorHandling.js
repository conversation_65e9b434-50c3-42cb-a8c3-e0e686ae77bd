/**
 * Error Handling Utilities
 * 
 * Centralized error handling functions for categorizing errors and creating user-friendly messages.
 * Extracted from errorHandlingService.js to eliminate duplication.
 */

import { createLogger } from './logger.js';

// Create logger for this module
const logger = createLogger('ErrorHandling');

/**
 * Error categories for classification
 */
export const ERROR_CATEGORIES = {
    VALIDATION: 'validation_error',
    NETWORK: 'network_error',
    TIMEOUT: 'timeout_error',
    FORMAT: 'format_error',
    SIZE: 'size_error',
    CLIENT: 'client_error',
    SERVER: 'server_error',
    UNKNOWN: 'unknown_error'
};

/**
 * Categorize error based on error properties and message
 * @param {Error} error - Error object to categorize
 * @returns {string} Error category
 */
export const categorizeError = (error) => {
    if (!error) {
        return ERROR_CATEGORIES.UNKNOWN;
    }

    const message = error.message?.toLowerCase() || '';
    const name = error.name?.toLowerCase() || '';

    // Timeout errors
    if (name === 'aborterror' || message.includes('timeout')) {
        return ERROR_CATEGORIES.TIMEOUT;
    }

    // Validation errors
    if (message.includes('invalid') || 
        message.includes('validation') ||
        message.includes('required') ||
        message.includes('missing')) {
        return ERROR_CATEGORIES.VALIDATION;
    }

    // Format errors
    if (message.includes('format') || 
        message.includes('unsupported') ||
        message.includes('content type')) {
        return ERROR_CATEGORIES.FORMAT;
    }

    // Size errors
    if (message.includes('too large') || 
        message.includes('size') ||
        message.includes('limit exceeded')) {
        return ERROR_CATEGORIES.SIZE;
    }

    // HTTP status-based categorization
    if (error.status) {
        if (error.status >= 400 && error.status < 500) {
            return ERROR_CATEGORIES.CLIENT;
        }
        if (error.status >= 500) {
            return ERROR_CATEGORIES.SERVER;
        }
    }

    // Network errors
    if (message.includes('fetch') || 
        message.includes('network') ||
        message.includes('connection') ||
        name === 'typeerror') {
        return ERROR_CATEGORIES.NETWORK;
    }

    return ERROR_CATEGORIES.UNKNOWN;
};

/**
 * Create user-friendly error message based on error category
 * @param {Error} error - Error object
 * @param {Object} context - Additional context
 * @returns {string} User-friendly error message
 */
export const createUserFriendlyErrorMessage = (error, context = {}) => {
    const category = categorizeError(error);
    const operation = context.operation || 'operation';

    switch (category) {
        case ERROR_CATEGORIES.VALIDATION:
            return `The ${operation} failed due to invalid data. Please check your input and try again.`;

        case ERROR_CATEGORIES.NETWORK:
            return `Network connection failed during ${operation}. Please check your internet connection and try again.`;

        case ERROR_CATEGORIES.TIMEOUT:
            return `The ${operation} timed out. This might be due to a slow connection or server issues. Please try again.`;

        case ERROR_CATEGORIES.FORMAT:
            return `The file format is not supported for this ${operation}. Please use a supported format and try again.`;

        case ERROR_CATEGORIES.SIZE:
            return `The file is too large for this ${operation}. Please use a smaller file and try again.`;

        case ERROR_CATEGORIES.CLIENT:
            if (error.status === 404) {
                return `The requested resource was not found. Please check the URL and try again.`;
            }
            if (error.status === 403) {
                return `Access denied. You don't have permission to perform this ${operation}.`;
            }
            return `There was a problem with your request. Please check your input and try again.`;

        case ERROR_CATEGORIES.SERVER:
            return `The server is experiencing issues. Please try again later.`;

        case ERROR_CATEGORIES.UNKNOWN:
        default:
            return `An unexpected error occurred during ${operation}. Please try again, and contact support if the problem persists.`;
    }
};

/**
 * Determine if an error should be retried
 * @param {Error} error - Error object
 * @param {number} retryCount - Current retry count
 * @param {number} maxRetries - Maximum retry attempts
 * @returns {boolean} Whether the error should be retried
 */
export const shouldRetryError = (error, retryCount, maxRetries = 3) => {
    // Don't retry if we've exceeded max retries
    if (retryCount >= maxRetries) {
        return false;
    }

    // Don't retry if explicitly marked as non-retryable
    if (error.retryable === false) {
        return false;
    }

    const category = categorizeError(error);

    // Don't retry validation, format, or size errors
    if ([ERROR_CATEGORIES.VALIDATION, ERROR_CATEGORIES.FORMAT, ERROR_CATEGORIES.SIZE].includes(category)) {
        return false;
    }

    // Don't retry client errors (except for specific cases)
    if (category === ERROR_CATEGORIES.CLIENT) {
        // Retry rate limiting (429) and some 4xx errors
        return error.status === 429 || error.status === 408;
    }

    // Retry network, timeout, and server errors
    return [ERROR_CATEGORIES.NETWORK, ERROR_CATEGORIES.TIMEOUT, ERROR_CATEGORIES.SERVER].includes(category);
};

/**
 * Create comprehensive error summary for user display
 * @param {Array} errors - Array of error messages
 * @param {Array} warnings - Array of warning messages
 * @param {Object} context - Additional context information
 * @returns {Object} Error summary with user-friendly messages
 */
export const createErrorSummary = (errors = [], warnings = [], context = {}) => {
    const summary = {
        hasErrors: errors.length > 0,
        hasWarnings: warnings.length > 0,
        errorCount: errors.length,
        warningCount: warnings.length,
        userMessage: '',
        technicalDetails: {
            errors,
            warnings,
            context
        }
    };

    // Create user-friendly summary message
    if (summary.hasErrors) {
        if (errors.length === 1) {
            summary.userMessage = `An error occurred: ${errors[0]}`;
        } else {
            summary.userMessage = `${errors.length} errors occurred. Please review the details and try again.`;
        }
    } else if (summary.hasWarnings) {
        if (warnings.length === 1) {
            summary.userMessage = `Warning: ${warnings[0]}`;
        } else {
            summary.userMessage = `${warnings.length} warnings were encountered. The operation completed but please review the details.`;
        }
    } else {
        summary.userMessage = 'Operation completed successfully.';
    }

    return summary;
};

/**
 * Generate image-specific error message
 * @param {Object} imageResult - Image processing result
 * @returns {string} Formatted error message
 */
export const generateImageErrorMessage = (imageResult) => {
    if (!imageResult || !imageResult.failedImages) {
        return 'Unknown image processing error occurred.';
    }

    const { failedImages, totalImages, successCount } = imageResult;
    const failureCount = failedImages.length;

    if (failureCount === 0) {
        return `All ${totalImages} images processed successfully.`;
    }

    if (successCount === 0) {
        return `Failed to process any of the ${totalImages} images. Please check the image URLs and try again.`;
    }

    const errorTypes = {};
    failedImages.forEach(img => {
        const category = categorizeError({ message: img.error });
        errorTypes[category] = (errorTypes[category] || 0) + 1;
    });

    const errorSummary = Object.entries(errorTypes)
        .map(([type, count]) => `${count} ${type.replace('_', ' ')} error${count > 1 ? 's' : ''}`)
        .join(', ');

    return `${successCount}/${totalImages} images processed successfully. ${failureCount} failed: ${errorSummary}.`;
};

/**
 * Create detailed error report for logging
 * @param {Object} result - Processing result with errors
 * @returns {Object} Detailed error report
 */
export const createDetailedErrorReport = (result) => {
    const report = {
        timestamp: new Date().toISOString(),
        summary: {
            total: result.totalImages || 0,
            successful: result.successCount || 0,
            failed: result.failureCount || 0
        },
        errors: {},
        failedItems: []
    };

    if (result.failedImages) {
        result.failedImages.forEach(item => {
            const category = categorizeError({ message: item.error });
            
            if (!report.errors[category]) {
                report.errors[category] = {
                    count: 0,
                    examples: []
                };
            }
            
            report.errors[category].count++;
            
            if (report.errors[category].examples.length < 3) {
                report.errors[category].examples.push({
                    url: item.src || item.url,
                    error: item.error
                });
            }

            report.failedItems.push({
                url: item.src || item.url,
                error: item.error,
                category
            });
        });
    }

    return report;
};

/**
 * Log error with appropriate level based on severity
 * @param {Error} error - Error to log
 * @param {Object} context - Additional context
 */
export const logError = (error, context = {}) => {
    const category = categorizeError(error);
    
    // Log validation and format errors as warnings (user errors)
    if ([ERROR_CATEGORIES.VALIDATION, ERROR_CATEGORIES.FORMAT].includes(category)) {
        logger.warn(error.message, { error, context, category });
    } else {
        // Log other errors as errors (system issues)
        logger.error(error.message, { error, context, category });
    }
};

// Export all error handling functions
export default {
    categorizeError,
    createUserFriendlyErrorMessage,
    shouldRetryError,
    createErrorSummary,
    generateImageErrorMessage,
    createDetailedErrorReport,
    logError,
    ERROR_CATEGORIES
};
