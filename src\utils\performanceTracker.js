// Performance Tracker Utility
// This utility helps track and log performance metrics for critical operations

class PerformanceTracker {
  constructor() {
    this.marks = new Map();
    this.measures = [];
    this.enabled = true;
  }

  // Start tracking a specific operation
  start(operationName) {
    if (!this.enabled) return;
    
    const startTime = performance.now();
    this.marks.set(operationName, {
      start: startTime,
      end: null
    });
    
    console.log(`⏱️ [${operationName}] Started`);
    return startTime;
  }

  // End tracking a specific operation
  end(operationName) {
    if (!this.enabled || !this.marks.has(operationName)) return;
    
    const endTime = performance.now();
    const mark = this.marks.get(operationName);
    mark.end = endTime;
    
    const duration = endTime - mark.start;
    this.measures.push({
      name: operationName,
      duration,
      timestamp: new Date().toISOString()
    });
    
    console.log(`⏱️ [${operationName}] Completed in ${duration.toFixed(2)}ms`);
    return duration;
  }

  // Get all performance measures
  getMeasures() {
    return [...this.measures];
  }

  // Get specific measure by name
  getMeasure(operationName) {
    return this.measures.find(m => m.name === operationName);
  }

  // Clear all tracking data
  clear() {
    this.marks.clear();
    this.measures = [];
  }

  // Enable or disable tracking
  setEnabled(enabled) {
    this.enabled = enabled;
  }

  // Log all measures to console (development only)
  logMeasures() {
    if (!import.meta.env.DEV) {
      return;
    }

    if (this.measures.length === 0) {
      console.log('No performance measures recorded');
      return;
    }

    console.log('📊 Performance Measures:');
    console.table(this.measures.map(m => ({
      Operation: m.name,
      'Duration (ms)': m.duration.toFixed(2),
      Timestamp: m.timestamp
    })));

    // Find potential bottlenecks
    const sortedMeasures = [...this.measures].sort((a, b) => b.duration - a.duration);
    if (sortedMeasures.length > 0) {
      console.log('⚠️ Potential bottlenecks:');
      sortedMeasures.slice(0, 3).forEach((m, i) => {
        console.log(`${i + 1}. ${m.name}: ${m.duration.toFixed(2)}ms`);
      });
    }
  }
}

// Create singleton instance
export const performanceTracker = new PerformanceTracker();

// Make available globally in development
if (import.meta.env.DEV) {
  window.performanceTracker = performanceTracker;
}

export default performanceTracker;