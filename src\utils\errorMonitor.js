/**
 * Error Monitoring Service
 * 
 * A centralized utility for capturing, logging, and reporting errors consistently
 * throughout the application. Provides context-aware error reporting and
 * maintains an in-memory log of recent errors.
 */

// Configuration options for the error monitor
const DEFAULT_CONFIG = {
    maxStoredErrors: 50,
    consoleOutput: import.meta.env.DEV, // Only log to console in development
    logLevel: 'error', // 'debug', 'info', 'warn', 'error'
    contextDataLimit: 10000, // Limit context data size in characters
};

// Severity levels for errors
export const ErrorSeverity = {
    DEBUG: 'debug',
    INFO: 'info',
    WARNING: 'warning',
    ERROR: 'error',
    CRITICAL: 'critical',
};

/**
 * ErrorMonitor class for centralized error handling
 */
class ErrorMonitor {
    constructor(config = {}) {
        this.config = { ...DEFAULT_CONFIG, ...config };
        this.errors = [];
        this.initialized = false;
        this.errorListeners = [];
    }

    /**
     * Initialize the error monitor with global handlers
     */
    initialize() {
        if (this.initialized) return;

        // Set up global unhandled error listener
        window.addEventListener('error', (event) => {
            this.captureError(event.error || new Error(event.message), {
                source: 'window.onerror',
                type: 'uncaught_error',
                location: {
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                },
            });

            // Don't prevent default handling
            return false;
        });

        // Set up unhandled promise rejection listener
        window.addEventListener('unhandledrejection', (event) => {
            const error = event.reason instanceof Error
                ? event.reason
                : new Error(String(event.reason));

            this.captureError(error, {
                source: 'unhandledrejection',
                type: 'unhandled_promise_rejection',
                reason: event.reason,
            });
        });

        this.initialized = true;
        this.captureMessage('ErrorMonitor initialized', ErrorSeverity.INFO);
    }

    /**
     * Capture an error with context information
     * 
     * @param {Error} error - The error object
     * @param {Object} context - Additional context about where/how the error occurred
     * @param {string} severity - Error severity level
     * @returns {string} - ID of the captured error
     */
    captureError(error, context = {}, severity = ErrorSeverity.ERROR) {
        if (!error) {
            if (import.meta.env.DEV) {
                console.warn('Attempted to capture null or undefined error');
            }
            error = new Error('Unknown error');
        }

        // Ensure error is an Error object
        if (!(error instanceof Error)) {
            error = new Error(String(error));
        }

        // Generate a unique ID for this error
        const errorId = this._generateErrorId();

        // Sanitize and limit context data size
        const sanitizedContext = this._sanitizeContext(context);

        // Create error data object
        const errorData = {
            id: errorId,
            timestamp: new Date().toISOString(),
            message: error.message,
            name: error.name,
            stack: error.stack,
            severity,
            context: sanitizedContext,
            url: window.location.href,
            userAgent: navigator.userAgent,
        };

        // Add to in-memory store
        this.errors.unshift(errorData);

        // Trim error list if it exceeds max size
        if (this.errors.length > this.config.maxStoredErrors) {
            this.errors = this.errors.slice(0, this.config.maxStoredErrors);
        }

        // Log to console if enabled
        if (this.config.consoleOutput) {
            this._logToConsole(errorData);
        }

        // Notify any listeners
        this._notifyListeners(errorData);

        return errorId;
    }

    /**
     * Capture a message as an info/debug entry
     * 
     * @param {string} message - The message to capture
     * @param {string} severity - Severity level
     * @param {Object} context - Additional context
     * @returns {string} - ID of the captured message
     */
    captureMessage(message, severity = ErrorSeverity.INFO, context = {}) {
        const error = new Error(message);
        return this.captureError(error, context, severity);
    }

    /**
     * Get recent errors, optionally filtered by severity
     * 
     * @param {Object} options - Filter options
     * @param {number} options.limit - Maximum number of errors to return
     * @param {string} options.severity - Filter by minimum severity
     * @param {string} options.source - Filter by error source
     * @returns {Array} - Array of error objects
     */
    getRecentErrors({ limit = 10, severity = null, source = null } = {}) {
        let filteredErrors = [...this.errors];

        // Filter by severity if specified
        if (severity) {
            const severityLevels = Object.values(ErrorSeverity);
            const severityIndex = severityLevels.indexOf(severity);

            if (severityIndex >= 0) {
                filteredErrors = filteredErrors.filter(error => {
                    const errorSeverityIndex = severityLevels.indexOf(error.severity);
                    return errorSeverityIndex >= severityIndex;
                });
            }
        }

        // Filter by source if specified
        if (source) {
            filteredErrors = filteredErrors.filter(error =>
                error.context && error.context.source === source
            );
        }

        // Limit results
        return filteredErrors.slice(0, limit);
    }

    /**
     * Clear all stored errors
     */
    clearErrors() {
        this.errors = [];
        this.captureMessage('Error log cleared', ErrorSeverity.INFO);
    }

    /**
     * Add a listener for new errors
     * 
     * @param {Function} listener - Function to call when new errors are captured
     * @returns {Function} - Function to remove the listener
     */
    addErrorListener(listener) {
        if (typeof listener !== 'function') {
            throw new Error('Error listener must be a function');
        }

        this.errorListeners.push(listener);

        // Return function to remove this listener
        return () => {
            this.errorListeners = this.errorListeners.filter(l => l !== listener);
        };
    }

    /**
     * Create a context-bound logger for a specific component or feature
     * 
     * @param {string} componentName - Name of the component or feature
     * @param {Object} defaultContext - Default context to include with all logs
     * @returns {Object} - Bound logger methods
     */
    createContextLogger(componentName, defaultContext = {}) {
        const baseContext = {
            component: componentName,
            ...defaultContext
        };

        return {
            debug: (message, context = {}) =>
                this.captureMessage(message, ErrorSeverity.DEBUG, { ...baseContext, ...context }),

            info: (message, context = {}) =>
                this.captureMessage(message, ErrorSeverity.INFO, { ...baseContext, ...context }),

            warn: (message, context = {}) =>
                this.captureMessage(message, ErrorSeverity.WARNING, { ...baseContext, ...context }),

            error: (error, context = {}) =>
                this.captureError(error, { ...baseContext, ...context }),

            critical: (error, context = {}) =>
                this.captureError(error, { ...baseContext, ...context }, ErrorSeverity.CRITICAL),
        };
    }

    /**
     * Generate a unique ID for an error
     * @private
     */
    _generateErrorId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
    }

    /**
     * Sanitize context object to prevent circular references and limit size
     * @private
     */
    _sanitizeContext(context) {
        try {
            // Convert to JSON and back to remove circular references
            const jsonString = JSON.stringify(context);

            // Check if the context is too large
            if (jsonString.length > this.config.contextDataLimit) {
                return {
                    _truncated: true,
                    _originalSize: jsonString.length,
                    summary: `Context data exceeded size limit (${this.config.contextDataLimit} chars)`
                };
            }

            return JSON.parse(jsonString);
        } catch (e) {
            return {
                _error: 'Could not serialize context data',
                _reason: e.message
            };
        }
    }

    /**
     * Log error data to console
     * @private
     */
    _logToConsole(errorData) {
        const { severity, message, context, stack } = errorData;

        // Use appropriate console method based on severity
        let logMethod;
        switch (severity) {
            case ErrorSeverity.DEBUG:
                logMethod = console.debug;
                break;
            case ErrorSeverity.INFO:
                logMethod = console.info;
                break;
            case ErrorSeverity.WARNING:
                logMethod = console.warn;
                break;
            case ErrorSeverity.CRITICAL:
                logMethod = console.error;
                break;
            case ErrorSeverity.ERROR:
            default:
                logMethod = console.error;
        }

        // Format the log message
        const componentInfo = context && context.component ? `[${context.component}] ` : '';
        logMethod(
            `%c${severity.toUpperCase()}%c ${componentInfo}${message}`,
            `background: ${this._getSeverityColor(severity)}; color: white; padding: 2px 4px; border-radius: 2px;`,
            'color: inherit',
            '\n',
            context,
            '\n',
            stack
        );
    }

    /**
     * Get color for severity level
     * @private
     */
    _getSeverityColor(severity) {
        switch (severity) {
            case ErrorSeverity.DEBUG: return '#6c757d';
            case ErrorSeverity.INFO: return '#0d6efd';
            case ErrorSeverity.WARNING: return '#ffc107';
            case ErrorSeverity.ERROR: return '#dc3545';
            case ErrorSeverity.CRITICAL: return '#7a0000';
            default: return '#6c757d';
        }
    }

    /**
     * Notify all listeners about a new error
     * @private
     */
    _notifyListeners(errorData) {
        this.errorListeners.forEach(listener => {
            try {
                listener(errorData);
            } catch (e) {
                if (import.meta.env.DEV) {
                    console.error('Error in error listener:', e);
                }
            }
        });
    }
}

// Create and export a singleton instance
const errorMonitor = new ErrorMonitor();

// Initialize in browser environments
if (typeof window !== 'undefined') {
    errorMonitor.initialize();
}

export default errorMonitor;