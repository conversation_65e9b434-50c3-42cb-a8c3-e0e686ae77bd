/**
 * Storage Management Service
 * Comprehensive localStorage and database synchronization with cleanup management
 */

import { projectsService } from './projectsService';

// Storage configuration
const STORAGE_CONFIG = {
  // Size limits (in bytes)
  MAX_TOTAL_STORAGE: 8 * 1024 * 1024, // 8MB (conservative limit)
  WARNING_THRESHOLD: 6 * 1024 * 1024, // 6MB warning
  CLEANUP_THRESHOLD: 7 * 1024 * 1024, // 7MB cleanup trigger
  
  // Retention policies
  ABANDONED_DOCUMENT_DAYS: 7, // Clean up documents not accessed for 7 days
  COMPLETED_DOCUMENT_RETENTION_DAYS: 3, // Keep completed docs for 3 days
  
  // Sync configuration
  AUTO_SYNC_INTERVAL: 30000, // 30 seconds
  BATCH_SYNC_SIZE: 5, // Max documents to sync at once
  
  // Storage keys
  STORAGE_METADATA_KEY: 'docforge_storage_metadata',
  SYNC_QUEUE_KEY: 'docforge_sync_queue'
};

class StorageManagementService {
  constructor() {
    this.syncQueue = new Set();
    this.isInitialized = false;
    this.syncInterval = null;
    this.storageMetadata = this.loadStorageMetadata();
  }

  /**
   * Initialize the storage management service
   */
  async initialize() {
    if (this.isInitialized) return;

    console.log('🔧 Initializing Storage Management Service');
    
    try {
      // Load existing sync queue
      this.loadSyncQueue();
      
      // Perform initial cleanup
      await this.performMaintenanceCleanup();
      
      // Start periodic sync
      this.startPeriodicSync();
      
      // Monitor storage size
      this._monitorStorageSize();
      
      this.isInitialized = true;
      console.log('✅ Storage Management Service initialized');
      
    } catch (error) {
      console.error('❌ Failed to initialize Storage Management Service:', error);
    }
  }

  /**
   * Get current storage usage statistics (internal use only)
   */
  _getStorageStats() {
    let totalSize = 0;
    let documentCount = 0;
    const documents = [];

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('document-')) {
        try {
          const data = localStorage.getItem(key);
          const size = new Blob([data]).size;
          totalSize += size;
          documentCount++;

          const documentData = JSON.parse(data);
          documents.push({
            id: key.replace('document-', ''),
            size,
            lastModified: documentData.lastModified,
            createdAt: documentData.createdAt,
            hasGeneratedContent: !!documentData.generatedContent
          });
        } catch (error) {
          // Silently handle corrupted documents
          this._handleCorruptedDocument(key);
        }
      }
    }

    return {
      totalSize,
      documentCount,
      documents: documents.sort((a, b) => new Date(b.lastModified) - new Date(a.lastModified)),
      needsCleanup: totalSize > STORAGE_CONFIG.CLEANUP_THRESHOLD
    };
  }

  /**
   * Add document to sync queue for database synchronization
   */
  addToSyncQueue(documentId) {
    this.syncQueue.add(documentId);
    this.saveSyncQueue();

    // Update document metadata
    this.updateDocumentMetadata(documentId, {
      lastActivity: new Date().toISOString(),
      syncStatus: 'pending'
    });

    // Trigger storage monitoring after each save
    this._monitorStorageSize();
  }

  /**
   * Mark document as completed and schedule for cleanup
   */
  markDocumentCompleted(documentId) {
    this.updateDocumentMetadata(documentId, {
      status: 'completed',
      completedAt: new Date().toISOString(),
      cleanupScheduledAt: new Date(Date.now() + STORAGE_CONFIG.COMPLETED_DOCUMENT_RETENTION_DAYS * 24 * 60 * 60 * 1000).toISOString()
    });

    // Immediate sync to database before cleanup
    this.addToSyncQueue(documentId);
  }

  /**
   * Mark document as published and clean up localStorage
   */
  async markDocumentPublished(documentId) {
    // Ensure final sync to database
    await this.syncDocumentToDatabase(documentId);

    // Clean up from localStorage after successful publish
    await this._cleanupDocument(documentId, 'published');
  }

  /**
   * Sync document content to database
   */
  async syncDocumentToDatabase(documentId) {
    try {
      const documentKey = `document-${documentId}`;
      const localData = localStorage.getItem(documentKey);
      
      if (!localData) {
        console.warn(`No localStorage data found for document ${documentId}`);
        return { success: false, error: 'No local data' };
      }

      const documentData = JSON.parse(localData);
      
      // Prepare data for database sync
      const syncData = {
        generated_content: documentData.generatedContent || null,
        questionnaire_data: documentData,
        word_count: documentData.generatedContent?.wordCount || 0,
        chapter_count: documentData.generatedContent?.chapters?.length || 0,
        updated_at: new Date().toISOString()
      };

      // Update project in database
      const result = await projectsService.updateProject(documentId, syncData);
      
      if (result.success) {
        // Update local metadata
        this.updateDocumentMetadata(documentId, {
          lastSyncedAt: new Date().toISOString(),
          syncStatus: 'synced'
        });
        
        console.log(`✅ Document ${documentId} synced to database`);
        return { success: true };
      } else {
        console.error(`❌ Failed to sync document ${documentId}:`, result.error);
        return { success: false, error: result.error };
      }
      
    } catch (error) {
      console.error(`❌ Error syncing document ${documentId}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Perform batch sync of queued documents
   */
  async performBatchSync() {
    if (this.syncQueue.size === 0) return;

    const documentsToSync = Array.from(this.syncQueue).slice(0, STORAGE_CONFIG.BATCH_SYNC_SIZE);
    console.log(`🔄 Syncing ${documentsToSync.length} documents to database`);

    const results = await Promise.allSettled(
      documentsToSync.map(docId => this.syncDocumentToDatabase(docId))
    );

    // Remove successfully synced documents from queue
    results.forEach((result, index) => {
      const documentId = documentsToSync[index];
      if (result.status === 'fulfilled' && result.value.success) {
        this.syncQueue.delete(documentId);
      }
    });

    this.saveSyncQueue();
    
    const successCount = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
    console.log(`✅ Batch sync completed: ${successCount}/${documentsToSync.length} successful`);
  }

  /**
   * Clean up abandoned and old documents (automatic, silent operation)
   */
  async performMaintenanceCleanup() {
    try {
      const stats = this._getStorageStats();
      const now = new Date();
      let cleanedCount = 0;

      for (const doc of stats.documents) {
        const lastModified = new Date(doc.lastModified);
        const daysSinceModified = (now - lastModified) / (1000 * 60 * 60 * 24);

        let shouldClean = false;
        let reason = '';

        // Check if document is abandoned
        if (daysSinceModified > STORAGE_CONFIG.ABANDONED_DOCUMENT_DAYS) {
          shouldClean = true;
          reason = 'abandoned';
        }

        // Check if storage is near limit and this is an old document
        if (stats.needsCleanup && daysSinceModified > 1) {
          shouldClean = true;
          reason = 'storage_limit';
        }

        if (shouldClean) {
          await this._cleanupDocument(doc.id, reason);
          cleanedCount++;
        }
      }

      // Only log if cleanup actually happened
      if (cleanedCount > 0) {
        console.log(`🧹 Auto-cleanup: ${cleanedCount} documents cleaned`);
      }
    } catch (error) {
      // Silent error handling - don't disrupt user experience
      console.warn('Storage cleanup error:', error);
    }
  }

  /**
   * Clean up a specific document (internal method)
   */
  async _cleanupDocument(documentId, reason = 'manual') {
    try {
      // First, try to sync to database if not already synced
      const metadata = this.getDocumentMetadata(documentId);
      if (!metadata.lastSyncedAt) {
        console.log(`📤 Syncing document ${documentId} before cleanup`);
        await this.syncDocumentToDatabase(documentId);
      }

      // Remove from localStorage
      const documentKey = `document-${documentId}`;
      localStorage.removeItem(documentKey);
      
      // Remove from sync queue
      this.syncQueue.delete(documentId);
      this.saveSyncQueue();

      // Update metadata
      this.removeDocumentMetadata(documentId);

      // Silent cleanup - only log in debug mode
      if (process.env.NODE_ENV === 'development') {
        console.log(`🗑️ Auto-cleaned document ${documentId} (${reason})`);
      }
      return true;
      
    } catch (error) {
      console.error(`❌ Failed to cleanup document ${documentId}:`, error);
      return false;
    }
  }

  /**
   * Start periodic sync process
   */
  startPeriodicSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(async () => {
      try {
        await this.performBatchSync();

        // Also perform maintenance cleanup every 5 sync cycles
        if (Math.random() < 0.2) { // 20% chance each cycle = roughly every 5 cycles
          await this.performMaintenanceCleanup();
        }
      } catch (error) {
        // Silent error handling - don't disrupt user experience
        if (process.env.NODE_ENV === 'development') {
          console.error('Periodic sync error:', error);
        }
      }
    }, STORAGE_CONFIG.AUTO_SYNC_INTERVAL);
  }

  /**
   * Stop periodic sync
   */
  stopPeriodicSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      console.log('⏹️ Periodic sync stopped');
    }
  }

  /**
   * Monitor storage size and trigger cleanup if needed (silent operation)
   */
  _monitorStorageSize() {
    const stats = this._getStorageStats();

    if (stats.needsCleanup) {
      // Silently trigger cleanup without user notification
      this.performMaintenanceCleanup();
    }
  }

  /**
   * Handle corrupted document data
   */
  _handleCorruptedDocument(documentKey) {
    try {
      localStorage.removeItem(documentKey);
      const documentId = documentKey.replace('document-', '');
      this.removeDocumentMetadata(documentId);

      if (process.env.NODE_ENV === 'development') {
        console.warn(`🔧 Removed corrupted document: ${documentKey}`);
      }
    } catch (error) {
      // Silent error handling
    }
  }

  // Utility methods for metadata management
  loadStorageMetadata() {
    try {
      const data = localStorage.getItem(STORAGE_CONFIG.STORAGE_METADATA_KEY);
      return data ? JSON.parse(data) : {};
    } catch (error) {
      // Silent error handling in production - metadata will be regenerated
      if (import.meta.env.DEV) {
        console.warn('Failed to load storage metadata:', error);
      }
      return {};
    }
  }

  saveStorageMetadata() {
    try {
      localStorage.setItem(STORAGE_CONFIG.STORAGE_METADATA_KEY, JSON.stringify(this.storageMetadata));
    } catch (error) {
      console.error('Failed to save storage metadata:', error);
    }
  }

  updateDocumentMetadata(documentId, metadata) {
    this.storageMetadata[documentId] = {
      ...this.storageMetadata[documentId],
      ...metadata
    };
    this.saveStorageMetadata();
  }

  getDocumentMetadata(documentId) {
    return this.storageMetadata[documentId] || {};
  }

  removeDocumentMetadata(documentId) {
    delete this.storageMetadata[documentId];
    this.saveStorageMetadata();
  }

  loadSyncQueue() {
    try {
      const data = localStorage.getItem(STORAGE_CONFIG.SYNC_QUEUE_KEY);
      this.syncQueue = new Set(data ? JSON.parse(data) : []);
    } catch (error) {
      console.warn('Failed to load sync queue:', error);
      this.syncQueue = new Set();
    }
  }

  saveSyncQueue() {
    try {
      localStorage.setItem(STORAGE_CONFIG.SYNC_QUEUE_KEY, JSON.stringify([...this.syncQueue]));
    } catch (error) {
      console.error('Failed to save sync queue:', error);
    }
  }
}

// Create singleton instance
export const storageManager = new StorageManagementService();

// Auto-initialize when imported
if (typeof window !== 'undefined') {
  storageManager.initialize();
}

export default storageManager;
