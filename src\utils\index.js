/**
 * Utility Module Barrel Exports
 * 
 * Centralized exports for all utility modules to provide clean imports
 * and clear namespacing for utility functions.
 */

// Validation utilities
export {
    validateDocumentData,
    validateContent,
    validateImageUrl,
    validateImageFormat,
    validateFileSize,
    IMAGE_CONFIG
} from './validation.js';

// Image processing utilities
export {
    downloadImage,
    downloadImageWithRetry,
    shouldRetryImageDownload,
    categorizeImageError,
    calculateImageDimensions,
    convertImageForDocx
} from './imageProcessing.js';

// Content processing utilities
export {
    detectContentType,
    parseHTMLContent,
    parseMarkdownContent,
    convertMarkdownToHTML,
    processHTMLElement,
    extractImagesFromHTML,
    extractImagesFromMarkdown,
    CONTENT_TYPES,
    HEADING_LEVELS
} from './contentProcessing.js';

// Error handling utilities
export {
    categorizeError,
    createUserFriendlyErrorMessage,
    shouldRetryError,
    createErrorSummary,
    generateImageErrorMessage,
    createDetailedErrorReport,
    logError,
    ERROR_CATEGORIES
} from './errorHandling.js';

// Logger utilities
export {
    default as logger,
    debug,
    info,
    warn,
    error,
    critical,
    createLogger
} from './logger.js';

// Namespaced exports for organized imports
export const Validation = {
    validateDocumentData,
    validateContent,
    validateImageUrl,
    validateImageFormat,
    validateFileSize,
    IMAGE_CONFIG
};

export const ImageProcessing = {
    downloadImage,
    downloadImageWithRetry,
    shouldRetryImageDownload,
    categorizeImageError,
    calculateImageDimensions,
    convertImageForDocx
};

export const ContentProcessing = {
    detectContentType,
    parseHTMLContent,
    parseMarkdownContent,
    convertMarkdownToHTML,
    processHTMLElement,
    extractImagesFromHTML,
    extractImagesFromMarkdown,
    CONTENT_TYPES,
    HEADING_LEVELS
};

export const ErrorHandling = {
    categorizeError,
    createUserFriendlyErrorMessage,
    shouldRetryError,
    createErrorSummary,
    generateImageErrorMessage,
    createDetailedErrorReport,
    logError,
    ERROR_CATEGORIES
};

// Re-export existing utilities that are already properly organized
export { default as errorMonitor, ErrorSeverity } from './errorMonitor.js';
export { default as performanceTracker } from './performanceTracker.js';
export { performanceMonitor } from './performance.js';

// Default export with all utilities organized by category
export default {
    Validation,
    ImageProcessing,
    ContentProcessing,
    ErrorHandling,
    Logger: {
        logger,
        debug,
        info,
        warn,
        error,
        critical,
        createLogger
    },
    Monitoring: {
        errorMonitor,
        performanceTracker,
        performanceMonitor
    }
};
