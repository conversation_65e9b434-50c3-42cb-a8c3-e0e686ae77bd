import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tsconfigPaths from "vite-tsconfig-paths";

// https://vitejs.dev/config/
export default defineConfig({
  // This changes the out put dir from dist to build
  // comment this out if that isn't relevant for your project
  build: {
    outDir: "build",
    chunkSizeWarningLimit: 2000,
    rollupOptions: {
      external: [],
      output: {
        manualChunks: (id) => {
          // Vendor libraries
          if (id.includes('node_modules')) {
            // Large libraries get their own chunks
            if (id.includes('docx')) return 'docx-lib';
            if (id.includes('@tiptap')) return 'editor-lib';
            if (id.includes('unified') || id.includes('remark') || id.includes('rehype')) {
              return 'content-processing';
            }
            if (id.includes('react') || id.includes('react-dom')) return 'react-vendor';
            if (id.includes('react-router')) return 'router-vendor';
            if (id.includes('@supabase')) return 'supabase-vendor';
            if (id.includes('dompurify') || id.includes('mammoth')) return 'utils-vendor';

            // Everything else goes to vendor
            return 'vendor';
          }

          // App code splitting
          if (id.includes('/pages/')) {
            const pageName = id.split('/pages/')[1].split('/')[0];
            return `page-${pageName}`;
          }

          if (id.includes('/services/')) return 'services';
          if (id.includes('/utils/')) return 'utils';
          if (id.includes('/components/')) return 'components';
        }
      }
    }
  },
  plugins: [tsconfigPaths(), react()],
  server: {
    port: "4028",
    host: "0.0.0.0",
    strictPort: true,
    allowedHosts: [".amazonaws.com"],
  },
  resolve: {
    alias: {
      // Help resolve unsplash-js if needed
    }
  },
  optimizeDeps: {
    include: ['docx', 'mammoth', 'dompurify']
  },
  define: {
    global: 'globalThis',
  }
});
