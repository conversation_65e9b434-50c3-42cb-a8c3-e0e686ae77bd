import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tsconfigPaths from "vite-tsconfig-paths";

// https://vitejs.dev/config/
export default defineConfig({
  // This changes the out put dir from dist to build
  // comment this out if that isn't relevant for your project
  build: {
    outDir: "build",
    chunkSizeWarningLimit: 2000,
    rollupOptions: {
      external: [],
      output: {
        manualChunks: {
          // Separate heavy libraries into their own chunks
          'docx-lib': ['docx'],
          'editor-lib': ['@tiptap/core', '@tiptap/react', '@tiptap/starter-kit'],
          'content-processing': ['unified', 'remark-parse', 'remark-rehype', 'rehype-stringify']
        }
      }
    }
  },
  plugins: [tsconfigPaths(), react()],
  server: {
    port: "4028",
    host: "0.0.0.0",
    strictPort: true,
    allowedHosts: [".amazonaws.com"],
  },
  resolve: {
    alias: {
      // Help resolve unsplash-js if needed
    }
  },
  optimizeDeps: {
    include: ['docx', 'mammoth', 'dompurify']
  },
  define: {
    global: 'globalThis',
  }
});
